### 能量奖励规则管理 API 测试

# 设置变量
@baseUrl = http://localhost:3004
@adminToken = YOUR_ADMIN_TOKEN_HERE

### 1. 获取能量奖励规则列表（支持分页和筛选）
GET {{baseUrl}}/admin/plants/energy-rules?page=1&limit=10
Authorization: Bearer {{adminToken}}

### 2. 根据状态筛选规则（只获取启用的规则）
GET {{baseUrl}}/admin/plants/energy-rules?is_active=true&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 3. 根据规则类型筛选
GET {{baseUrl}}/admin/plants/energy-rules?rule_type=meditation_complete&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 4. 创建新的能量奖励规则
POST {{baseUrl}}/admin/plants/energy-rules
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "rule_name": "深度冥想奖励",
  "rule_type": "meditation_complete",
  "condition": {
    "min_duration": 3600,
    "meditation_type": "deep"
  },
  "energy_amount": 100,
  "bonus_multiplier": 2.0,
  "max_daily_times": 2,
  "description": "完成1小时深度冥想获得双倍奖励",
  "priority": 15
}

### 5. 获取指定规则详情
GET {{baseUrl}}/admin/plants/energy-rules/1
Authorization: Bearer {{adminToken}}

### 6. 更新能量奖励规则
PUT {{baseUrl}}/admin/plants/energy-rules/1
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "rule_name": "完成冥想任务（更新）",
  "energy_amount": 25,
  "bonus_multiplier": 1.2,
  "description": "完成5分钟以上冥想获得基础奖励（已更新）",
  "priority": 2
}

### 7. 切换规则状态（启用/禁用）
PUT {{baseUrl}}/admin/plants/energy-rules/1/toggle
Authorization: Bearer {{adminToken}}

### 8. 删除能量奖励规则
DELETE {{baseUrl}}/admin/plants/energy-rules/6
Authorization: Bearer {{adminToken}}

### 9. 获取所有禁用的规则
GET {{baseUrl}}/admin/plants/energy-rules?is_active=false
Authorization: Bearer {{adminToken}}

### 10. 创建特殊事件规则
POST {{baseUrl}}/admin/plants/energy-rules
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "rule_name": "新年特别奖励",
  "rule_type": "special_event",
  "condition": {
    "event_id": "new_year_2024",
    "start_date": "2024-01-01",
    "end_date": "2024-01-07"
  },
  "energy_amount": 500,
  "bonus_multiplier": 3.0,
  "max_daily_times": 1,
  "description": "新年期间每日登录获得特别奖励",
  "priority": 20,
  "is_active": false
}
