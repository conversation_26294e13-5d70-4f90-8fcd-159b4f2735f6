import KoaRouter from 'koa-router'
import controllers from '../controllers'
import { adminAuth, checkPermission } from '../middleware/adminAuth'

// 导入管理端控制器
import AdminController from '../controllers/admin'
import AdminUserController from '../controllers/adminUser'
import AdminUserLevelController from '../controllers/adminUserLevel'
import AdminMeditationController from '../controllers/adminMeditation'
import AdminTagController from '../controllers/adminTag'
import AdminPlantController from '../controllers/adminPlant'
import AdminPlantLevelController from '../controllers/adminPlantLevel'

const router = new KoaRouter()

export default router
  // ========================================
  // 管理员认证相关路由（无需认证）
  // ========================================
  .post('/admin/login', AdminController.login) // 管理员登录

  // ========================================
  // 管理员基础功能（需要管理员认证）
  // ========================================
  .get('/admin/profile', adminAuth, AdminController.getProfile) // 获取管理员信息

  // ========================================
  // 管理员管理（仅超级管理员）
  // ========================================
  .get('/admin/admins', adminAuth, checkPermission(['super_admin']), AdminController.getAdminList) // 获取管理员列表
  .post('/admin/admins', adminAuth, checkPermission(['super_admin']), AdminController.createAdmin) // 创建管理员
  .put('/admin/admins/:id', adminAuth, checkPermission(['super_admin']), AdminController.updateAdmin) // 更新管理员
  .delete('/admin/admins/:id', adminAuth, checkPermission(['super_admin']), AdminController.deleteAdmin) // 删除管理员

  // ========================================
  // 用户管理模块
  // ========================================
  .get('/admin/users', adminAuth, checkPermission(['super_admin', 'admin']), AdminUserController.getUserList) // 获取用户列表
  .get('/admin/users/statistics', adminAuth, checkPermission(['super_admin', 'admin']), AdminUserController.getUserStatistics) // 获取用户统计
  .get('/admin/users/:id', adminAuth, checkPermission(['super_admin', 'admin']), AdminUserController.getUserDetail) // 获取用户详情
  .put('/admin/users/:id/level', adminAuth, checkPermission(['super_admin', 'admin']), AdminUserController.updateUserLevel) // 更新用户等级

  // ========================================
  // 用户等级管理
  // ========================================
  .get('/admin/user-levels', adminAuth, checkPermission(['super_admin', 'admin']), AdminUserLevelController.getLevelConfigs) // 获取等级配置
  .post('/admin/user-levels', adminAuth, checkPermission(['super_admin', 'admin']), AdminUserLevelController.createLevelConfig) // 创建等级配置
  .put('/admin/user-levels/:id', adminAuth, checkPermission(['super_admin', 'admin']), AdminUserLevelController.updateLevelConfig) // 更新等级配置
  .delete('/admin/user-levels/:id', adminAuth, checkPermission(['super_admin', 'admin']), AdminUserLevelController.deleteLevelConfig) // 删除等级配置

  // ========================================
  // 冥想内容管理
  // ========================================
  .get('/admin/meditation/contents', adminAuth, AdminMeditationController.getContentList) // 获取内容列表
  .post('/admin/meditation/contents', adminAuth, checkPermission(['super_admin', 'admin', 'editor']), AdminMeditationController.createContent) // 创建内容
  .get('/admin/meditation/contents/:id', adminAuth, AdminMeditationController.getContentDetail) // 获取内容详情
  .put('/admin/meditation/contents/:id', adminAuth, checkPermission(['super_admin', 'admin', 'editor']), AdminMeditationController.updateContent) // 更新内容
  .delete('/admin/meditation/contents/:id', adminAuth, checkPermission(['super_admin', 'admin']), AdminMeditationController.deleteContent) // 删除内容

  // ========================================
  // 课程管理模块
  // ========================================
  .put('/admin/meditation/contents/:id/recommend', adminAuth, checkPermission(['super_admin', 'admin']), AdminMeditationController.toggleRecommend) // 推荐/取消推荐
  .put('/admin/meditation/contents/:id/status', adminAuth, checkPermission(['super_admin', 'admin', 'editor']), AdminMeditationController.updateStatus) // 修改状态（上架/下架）

  // 章节管理
  .get('/admin/meditation/courses/:courseId/chapters', adminAuth, AdminMeditationController.getCourseChapters) // 获取课程章节
  .post('/admin/meditation/courses/:courseId/chapters', adminAuth, checkPermission(['super_admin', 'admin', 'editor']), AdminMeditationController.addChapterToCourse) // 添加章节
  .delete('/admin/meditation/courses/:courseId/chapters/:chapterId', adminAuth, checkPermission(['super_admin', 'admin']), AdminMeditationController.removeChapterFromCourse) // 删除章节
  .put('/admin/meditation/courses/:courseId/chapters/reorder', adminAuth, checkPermission(['super_admin', 'admin', 'editor']), AdminMeditationController.reorderChapters) // 调整章节顺序

  // ========================================
  // 标签管理
  // ========================================
  .get('/admin/meditation/tags', adminAuth, AdminTagController.getTagList) // 获取标签列表
  .post('/admin/meditation/tags', adminAuth, checkPermission(['super_admin', 'admin', 'editor']), AdminTagController.createTag) // 创建标签
  .put('/admin/meditation/tags/:id', adminAuth, checkPermission(['super_admin', 'admin', 'editor']), AdminTagController.updateTag) // 更新标签
  .delete('/admin/meditation/tags/:id', adminAuth, checkPermission(['super_admin', 'admin']), AdminTagController.deleteTag) // 删除标签
  .delete('/admin/meditation/tags/batch-delete', adminAuth, checkPermission(['super_admin', 'admin']), AdminTagController.batchDeleteTags) // 批量删除标签

  // ========================================
  // 多肉品种管理
  // ========================================
  .get('/admin/plants/species', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantController.getSpeciesList) // 获取品种列表
  .post('/admin/plants/species', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantController.createSpecies) // 创建品种
  .put('/admin/plants/species/:id', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantController.updateSpecies) // 更新品种
  .delete('/admin/plants/species/:id', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantController.deleteSpecies) // 删除品种

  // ========================================
  // 多肉等级管理
  // ========================================
  .get('/admin/plants/levels', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantLevelController.getLevelsList) // 获取等级配置列表
  .get('/admin/plants/levels/:id', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantLevelController.getLevelDetail) // 获取等级配置详情
  .post('/admin/plants/levels', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantLevelController.createLevel) // 创建等级配置
  .put('/admin/plants/levels/:id', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantLevelController.updateLevel) // 更新等级配置
  .put('/admin/plants/levels/:id/toggle', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantLevelController.toggleLevelStatus) // 切换等级状态
  .delete('/admin/plants/levels/:id', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantLevelController.deleteLevel) // 删除等级配置

  // ========================================
  // 能量奖励管理
  // ========================================
  .get('/admin/plants/energy-rules', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantController.getEnergyRules) // 获取奖励规则
  .post('/admin/plants/energy-rules', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantController.createEnergyRule) // 创建奖励规则

  // ========================================
  // 统计数据
  // ========================================
  .get('/admin/plants/statistics', adminAuth, checkPermission(['super_admin', 'admin']), AdminPlantController.getPlantStatistics) // 获取多肉统计

  // ========================================
  // 文件上传（管理端）
  // ========================================
  .post('/admin/upload', adminAuth, controllers.upload) // 文件上传
