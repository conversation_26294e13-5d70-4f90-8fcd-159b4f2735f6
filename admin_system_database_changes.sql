-- ========================================
-- 后台管理系统数据库修改脚本
-- 创建时间: 2025-08-18
-- 说明: 为冥想应用添加后台管理系统所需的数据表和字段
-- ========================================

USE meditation_app;

-- ========================================
-- 1. 创建管理员表
-- ========================================
DROP TABLE IF EXISTS admins;
CREATE TABLE admins (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(64) NOT NULL UNIQUE COMMENT '管理员用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码（加密后）',
    real_name VARCHAR(64) DEFAULT NULL COMMENT '真实姓名',
    email VARCHAR(128) DEFAULT NULL COMMENT '邮箱',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    role ENUM('super_admin', 'admin', 'editor') DEFAULT 'editor' COMMENT '角色：超级管理员/管理员/编辑员',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态：激活/禁用',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- ========================================
-- 2. 创建用户等级配置表
-- ========================================
DROP TABLE IF EXISTS user_level_configs;
CREATE TABLE user_level_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    level INT NOT NULL UNIQUE COMMENT '等级',
    level_name VARCHAR(64) NOT NULL COMMENT '等级名称',
    required_days INT DEFAULT 0 COMMENT '所需坚持天数',
    required_duration INT DEFAULT 0 COMMENT '所需冥想总时长（秒）',
    benefits JSON DEFAULT NULL COMMENT '等级福利（JSON格式）',
    icon_url VARCHAR(512) DEFAULT NULL COMMENT '等级图标URL',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户等级配置表';

-- ========================================
-- 3. 创建多肉品种配置表
-- ========================================
DROP TABLE IF EXISTS plant_species;
CREATE TABLE plant_species (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(64) NOT NULL UNIQUE COMMENT '品种名称（英文标识）',
    display_name VARCHAR(64) NOT NULL COMMENT '显示名称（中文）',
    description TEXT DEFAULT NULL COMMENT '品种描述',
    rarity ENUM('common', 'rare', 'epic', 'legendary') DEFAULT 'common' COMMENT '稀有度',
    unlock_condition JSON DEFAULT NULL COMMENT '解锁条件（JSON格式）',
    growth_stages JSON DEFAULT NULL COMMENT '成长阶段配置（JSON格式）',
    max_level INT DEFAULT 10 COMMENT '最大等级',
    base_energy_per_level INT DEFAULT 100 COMMENT '每级所需基础能量',
    image_urls JSON DEFAULT NULL COMMENT '各阶段图片URL（JSON格式）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多肉品种配置表';

-- ========================================
-- 4. 创建能量奖励规则表
-- ========================================
DROP TABLE IF EXISTS energy_reward_rules;
CREATE TABLE energy_reward_rules (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    rule_name VARCHAR(128) NOT NULL COMMENT '规则名称',
    rule_type ENUM('meditation_complete', 'daily_streak', 'level_up', 'special_event') NOT NULL COMMENT '规则类型',
    condition_data JSON DEFAULT NULL COMMENT '触发条件（JSON格式）',
    energy_amount INT DEFAULT 0 COMMENT '奖励能量值',
    bonus_multiplier DECIMAL(3,2) DEFAULT 1.00 COMMENT '奖励倍数',
    max_daily_times INT DEFAULT 0 COMMENT '每日最大触发次数（0为无限制）',
    description TEXT DEFAULT NULL COMMENT '规则描述',
    priority INT DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='能量奖励规则表';

-- ========================================
-- 5. 修改现有表结构
-- ========================================

-- 为冥想内容表添加状态字段和音视频字段（字段已存在，跳过）
-- ALTER TABLE meditation_content
-- ADD COLUMN status ENUM('draft', 'published', 'archived') DEFAULT 'published' COMMENT '状态：草稿/已发布/已归档' AFTER favorite_count,
-- ADD COLUMN audio_url VARCHAR(512) DEFAULT NULL COMMENT '音频文件URL' AFTER cover_url,
-- ADD COLUMN video_url VARCHAR(512) DEFAULT NULL COMMENT '视频文件URL' AFTER audio_url;

-- 为多肉表添加品种关联（字段已存在，跳过）
-- ALTER TABLE plants
-- ADD COLUMN species_id BIGINT DEFAULT NULL COMMENT '品种ID' AFTER species,
-- ADD FOREIGN KEY (species_id) REFERENCES plant_species(id) ON DELETE SET NULL;

-- 更新现有多肉数据的品种关联
-- 临时禁用安全更新模式
SET SQL_SAFE_UPDATES = 0;
UPDATE plants SET species_id = 1 WHERE species = 'succulent';
-- 重新启用安全更新模式
SET SQL_SAFE_UPDATES = 1;

-- ========================================
-- 6. 创建索引优化查询性能
-- ========================================

-- 管理员表索引（忽略重复索引错误）
CREATE INDEX idx_admins_username ON admins(username);
CREATE INDEX idx_admins_status ON admins(status);
CREATE INDEX idx_admins_role ON admins(role);

-- 用户等级配置表索引
CREATE INDEX idx_user_level_configs_level ON user_level_configs(level);

-- 多肉品种配置表索引
CREATE INDEX idx_plant_species_name ON plant_species(name);
CREATE INDEX idx_plant_species_rarity ON plant_species(rarity);
CREATE INDEX idx_plant_species_is_active ON plant_species(is_active);

-- 能量奖励规则表索引
CREATE INDEX idx_energy_reward_rules_type ON energy_reward_rules(rule_type);
CREATE INDEX idx_energy_reward_rules_active ON energy_reward_rules(is_active);
CREATE INDEX idx_energy_reward_rules_priority ON energy_reward_rules(priority);

-- 冥想内容表新增索引
CREATE INDEX idx_meditation_content_status ON meditation_content(status);

-- 多肉表新增索引
CREATE INDEX idx_plants_species_id ON plants(species_id);

-- ========================================
-- 7. 插入默认数据
-- ========================================

-- 插入默认管理员账户
INSERT INTO admins (username, password, real_name, role, status) VALUES
('admin', '$2b$10$h6CvIJKvBhTqdRg7M9HB.O4uL4NlW4zuw7Qt6vin3FgIVOS60uU4S', '系统管理员', 'super_admin', 'active');

-- 插入默认用户等级配置
INSERT INTO user_level_configs (level, level_name, required_days, required_duration, benefits) VALUES
(1, '初心者', 0, 0, '{"description": "冥想入门", "features": ["基础多肉", "每日任务"]}'),
(2, '专注者', 3, 1800, '{"description": "坚持冥想3天", "features": ["解锁新多肉", "能量获取+20%"]}'),
(3, '修行者', 7, 7200, '{"description": "坚持冥想7天", "features": ["高级多肉", "能量获取+50%"]}'),
(4, '觉悟者', 21, 25200, '{"description": "坚持冥想21天", "features": ["稀有多肉", "能量获取+80%"]}'),
(5, '大师', 30, 54000, '{"description": "坚持冥想30天", "features": ["传说多肉", "能量获取+100%"]}');

-- 插入默认多肉品种配置
INSERT INTO plant_species (name, display_name, description, rarity, unlock_condition, growth_stages, max_level, base_energy_per_level, image_urls) VALUES
('succulent', '普通多肉', '最基础的多肉植物，适合新手培养', 'common', '{"level": 1}', '{"stages": [{"level": 1, "name": "幼苗"}, {"level": 5, "name": "成长期"}, {"level": 10, "name": "成熟期"}]}', 10, 50, '{"1": "/images/plants/succulent_1.png", "5": "/images/plants/succulent_5.png", "10": "/images/plants/succulent_10.png"}'),
('cactus', '仙人掌', '坚韧的沙漠植物，象征坚持不懈', 'common', '{"level": 2}', '{"stages": [{"level": 1, "name": "小刺球"}, {"level": 8, "name": "开花期"}, {"level": 15, "name": "巨型仙人掌"}]}', 15, 80, '{"1": "/images/plants/cactus_1.png", "8": "/images/plants/cactus_8.png", "15": "/images/plants/cactus_15.png"}'),
('jade_plant', '玉树', '寓意吉祥的多肉植物', 'rare', '{"level": 3, "days": 7}', '{"stages": [{"level": 1, "name": "嫩芽"}, {"level": 10, "name": "翠绿期"}, {"level": 20, "name": "玉树临风"}]}', 20, 120, '{"1": "/images/plants/jade_1.png", "10": "/images/plants/jade_10.png", "20": "/images/plants/jade_20.png"}');

-- 插入默认能量奖励规则
INSERT INTO energy_reward_rules (rule_name, rule_type, condition_data, energy_amount, bonus_multiplier, max_daily_times, description, priority) VALUES
('完成冥想任务', 'meditation_complete', '{"min_duration": 300}', 20, 1.0, 0, '完成5分钟以上冥想获得基础奖励', 1),
('长时间冥想', 'meditation_complete', '{"min_duration": 1800}', 50, 1.5, 3, '完成30分钟冥想获得额外奖励', 2),
('连续冥想3天', 'daily_streak', '{"streak_days": 3}', 100, 1.0, 1, '连续冥想3天获得奖励', 5),
('连续冥想7天', 'daily_streak', '{"streak_days": 7}', 300, 1.0, 1, '连续冥想7天获得大量奖励', 8),
('等级提升', 'level_up', '{}', 200, 1.0, 0, '用户等级提升时获得奖励', 10);

-- ========================================
-- 8. 完成数据库修改
-- ========================================

SELECT '后台管理系统数据库初始化完成！' as message;
SELECT '默认管理员账户：' as info;
SELECT 'username: admin' as username;
SELECT 'password: admin123' as password;
SELECT '请立即登录并修改默认密码！' as warning;
