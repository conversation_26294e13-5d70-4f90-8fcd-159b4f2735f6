const swaggerJSDoc = require('swagger-jsdoc')
const fs = require('fs')
const path = require('path')

// Swagger 配置选项
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: '双冥想小程序后端API',
      version: '1.0.0',
      description: '基于Koa2的冥想应用后端API接口文档',
    },
    servers: [
      {
        url: 'http://localhost:3004/api',
        description: '开发环境'
      }
    ],
    components: {
      securitySchemes: {
        Bearer: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT token, 格式: Bearer <token>'
        }
      },
      schemas: {
        // 通用响应结构
        ApiResponse: {
          type: 'object',
          properties: {
            code: {
              type: 'integer',
              description: '状态码'
            },
            message: {
              type: 'string',
              description: '响应消息'
            },
            data: {
              type: 'object',
              description: '响应数据'
            }
          }
        },
        // 分页响应结构
        PaginatedResponse: {
          type: 'object',
          properties: {
            code: {
              type: 'integer',
              description: '状态码'
            },
            data: {
              type: 'object',
              properties: {
                total: {
                  type: 'integer',
                  description: '总数量'
                },
                page: {
                  type: 'integer',
                  description: '当前页码'
                },
                limit: {
                  type: 'integer',
                  description: '每页数量'
                },
                items: {
                  type: 'array',
                  items: {
                    type: 'object'
                  },
                  description: '数据列表'
                }
              }
            }
          }
        },
        // 用户模型
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: '用户ID'
            },
            openid: {
              type: 'string',
              description: '微信openid'
            },
            unionid: {
              type: 'string',
              description: '微信unionid'
            },
            nickname: {
              type: 'string',
              description: '用户昵称'
            },
            avatar_url: {
              type: 'string',
              description: '头像URL'
            },
            meditation_level: {
              type: 'integer',
              description: '冥想等级'
            },
            streak_days: {
              type: 'integer',
              description: '连续天数'
            }
          }
        },
        // 冥想内容模型
        MeditationContent: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: '内容ID'
            },
            title: {
              type: 'string',
              description: '标题'
            },
            description: {
              type: 'string',
              description: '描述'
            },
            type: {
              type: 'string',
              enum: ['audio', 'video', 'text'],
              description: '内容类型'
            },
            sub_type: {
              type: 'string',
              description: '子类型'
            },
            duration: {
              type: 'integer',
              description: '时长(秒)'
            },
            cover_url: {
              type: 'string',
              description: '封面图片URL'
            },
            content_url: {
              type: 'string',
              description: '内容URL'
            },
            favorite_count: {
              type: 'integer',
              description: '收藏数'
            }
          }
        },
        // 多肉模型
        Plant: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              description: '多肉ID'
            },
            user_id: {
              type: 'integer',
              description: '用户ID'
            },
            species: {
              type: 'string',
              description: '品种'
            },
            energy_value: {
              type: 'integer',
              description: '能量值'
            },
            level: {
              type: 'integer',
              description: '等级'
            }
          }
        }
      }
    }
  },
  apis: [
    path.join(__dirname, '../src/controllers/*.js'),
    path.join(__dirname, '../src/routes/*.js')
  ]
}

// 生成 Swagger 规范
const swaggerSpec = swaggerJSDoc(swaggerOptions)

// 写入 JSON 文件
const outputPath = path.join(__dirname, '../swagger.json')
fs.writeFileSync(outputPath, JSON.stringify(swaggerSpec, null, 2))

console.log(`Swagger JSON 文件已生成: ${outputPath}`)
console.log('API 文档包含以下端点:')

// 打印所有路径
if (swaggerSpec.paths) {
  Object.keys(swaggerSpec.paths).forEach(path => {
    const methods = Object.keys(swaggerSpec.paths[path])
    methods.forEach(method => {
      const endpoint = swaggerSpec.paths[path][method]
      console.log(`  ${method.toUpperCase()} ${path} - ${endpoint.summary || '无描述'}`)
    })
  })
} else {
  console.log('  未找到任何 API 端点，请检查控制器中的 Swagger 注解')
}
