-- ========================================
-- 课程管理模块数据库修改脚本
-- 创建时间: 2025-08-22
-- 说明: 为冥想内容表添加课程管理所需的字段
-- ========================================

USE meditation_app;

-- ========================================
-- 1. 为冥想内容表添加课程管理字段
-- ========================================

-- 添加推荐状态字段
ALTER TABLE meditation_content 
ADD COLUMN is_recommended BOOLEAN DEFAULT FALSE COMMENT '是否推荐' AFTER status;

-- 添加排序字段
ALTER TABLE meditation_content 
ADD COLUMN sort_order INT DEFAULT 0 COMMENT '排序顺序' AFTER is_recommended;

-- ========================================
-- 2. 创建索引优化查询性能
-- ========================================

-- 推荐状态索引
CREATE INDEX idx_meditation_content_recommended ON meditation_content(is_recommended);

-- 排序字段索引
CREATE INDEX idx_meditation_content_sort_order ON meditation_content(sort_order);

-- 复合索引：状态+推荐+排序
CREATE INDEX idx_meditation_content_status_rec_sort ON meditation_content(status, is_recommended, sort_order);

-- 复合索引：类型+子类型+状态
CREATE INDEX idx_meditation_content_type_status ON meditation_content(type, sub_type, status);

-- ========================================
-- 3. 更新现有数据
-- ========================================

-- 为现有的课程内容设置默认排序
UPDATE meditation_content 
SET sort_order = id 
WHERE sort_order = 0;

-- 将一些热门内容设为推荐
UPDATE meditation_content 
SET is_recommended = TRUE 
WHERE favorite_count > 50;

-- ========================================
-- 4. 完成数据库修改
-- ========================================

SELECT '课程管理模块数据库字段添加完成！' as message;
