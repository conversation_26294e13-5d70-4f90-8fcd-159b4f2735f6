import { Op } from 'sequelize'
import PlantLevelConfig from '../models/PlantLevelConfig'
import { parsePaginationParams } from '../lib/utils'

export default class AdminPlantLevelController {
  /**
   * @swagger
   * /admin/plants/levels:
   *   get:
   *     tags:
   *       - 多肉等级管理
   *     summary: 获取多肉等级配置列表
   *     description: 获取所有多肉等级配置，支持分页和搜索
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: 搜索关键词（等级名称）
   *       - in: query
   *         name: is_active
   *         schema:
   *           type: boolean
   *         description: 是否启用状态筛选
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: 获取成功
   *                 data:
   *                   type: object
   *                   properties:
   *                     list:
   *                       type: array
   *                       items:
   *                         $ref: '#/components/schemas/PlantLevelConfig'
   *                     pagination:
   *                       $ref: '#/components/schemas/Pagination'
   */
  static async getLevelsList(ctx) {
    const { search, is_active } = ctx.query

    try {
      const { pageNum, pageSize, offset } = parsePaginationParams(ctx.query)
      const whereCondition = {}

      if (search) {
        whereCondition[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } }
        ]
      }

      if (is_active !== undefined) {
        whereCondition.is_active = is_active === 'true'
      }

      const levels = await PlantLevelConfig.findAndCountAll({
        where: whereCondition,
        limit: pageSize,
        offset: offset,
        order: [['sort_order', 'ASC'], ['level', 'ASC']]
      })

      // 解析JSON字段
      const levelsList = levels.rows.map(item => ({
        ...item.toJSON(),
        attribute_bonus: item.attribute_bonus ? JSON.parse(item.attribute_bonus) : null,
        special_ability: item.special_ability ? JSON.parse(item.special_ability) : null,
        unlock_reward: item.unlock_reward ? JSON.parse(item.unlock_reward) : null
      }))

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: {
          list: levelsList,
          pagination: {
            page: pageNum,
            limit: pageSize,
            total: levels.count,
            pages: Math.ceil(levels.count / pageSize)
          }
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取等级配置列表失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/levels/{id}:
   *   get:
   *     tags:
   *       - 多肉等级管理
   *     summary: 获取多肉等级配置详情
   *     description: 根据ID获取指定的多肉等级配置详情
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 等级配置ID
   *     responses:
   *       200:
   *         description: 获取成功
   *       404:
   *         description: 等级配置不存在
   */
  static async getLevelDetail(ctx) {
    const { id } = ctx.params

    try {
      const level = await PlantLevelConfig.findByPk(id)
      if (!level) {
        ctx.body = {
          code: 404,
          message: '等级配置不存在'
        }
        return
      }

      // 解析JSON字段
      const levelData = {
        ...level.toJSON(),
        attribute_bonus: level.attribute_bonus ? JSON.parse(level.attribute_bonus) : null,
        special_ability: level.special_ability ? JSON.parse(level.special_ability) : null,
        unlock_reward: level.unlock_reward ? JSON.parse(level.unlock_reward) : null
      }

      ctx.body = {
        code: 200,
        message: '获取成功',
        data: levelData
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取等级配置详情失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/levels:
   *   post:
   *     tags:
   *       - 多肉等级管理
   *     summary: 创建多肉等级配置
   *     description: 创建新的多肉等级配置
   *     security:
   *       - AdminBearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - level
   *               - name
   *               - required_energy
   *               - required_days
   *             properties:
   *               level:
   *                 type: integer
   *                 description: 等级数值
   *               name:
   *                 type: string
   *                 description: 等级名称
   *               icon:
   *                 type: string
   *                 description: 等级图标URL
   *               required_energy:
   *                 type: integer
   *                 description: 所需能量
   *               required_days:
   *                 type: integer
   *                 description: 所需天数
   *               attribute_bonus:
   *                 type: object
   *                 description: 属性加成配置
   *               special_ability:
   *                 type: object
   *                 description: 特殊能力配置
   *               unlock_reward:
   *                 type: object
   *                 description: 解锁奖励配置
   *               description:
   *                 type: string
   *                 description: 等级描述
   *               sort_order:
   *                 type: integer
   *                 description: 排序顺序
   *     responses:
   *       200:
   *         description: 创建成功
   *       400:
   *         description: 参数错误
   */
  static async createLevel(ctx) {
    const {
      level, name, icon, required_energy, required_days,
      attribute_bonus, special_ability, unlock_reward,
      description, sort_order
    } = ctx.request.body

    if (!level || !name || required_energy === undefined || required_days === undefined) {
      ctx.body = {
        code: 400,
        message: '等级、名称、所需能量和所需天数不能为空'
      }
      return
    }

    try {
      // 检查等级是否已存在
      const existingLevel = await PlantLevelConfig.findOne({ where: { level } })
      if (existingLevel) {
        ctx.body = {
          code: 400,
          message: '该等级已存在'
        }
        return
      }

      const levelConfig = await PlantLevelConfig.create({
        level,
        name,
        icon,
        required_energy,
        required_days,
        attribute_bonus: attribute_bonus ? JSON.stringify(attribute_bonus) : null,
        special_ability: special_ability ? JSON.stringify(special_ability) : null,
        unlock_reward: unlock_reward ? JSON.stringify(unlock_reward) : null,
        description,
        sort_order: sort_order || 0
      })

      ctx.body = {
        code: 200,
        message: '等级配置创建成功',
        data: levelConfig
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '创建等级配置失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/levels/{id}:
   *   put:
   *     tags:
   *       - 多肉等级管理
   *     summary: 更新多肉等级配置
   *     description: 更新指定的多肉等级配置
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 等级配置ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: 等级名称
   *               icon:
   *                 type: string
   *                 description: 等级图标URL
   *               required_energy:
   *                 type: integer
   *                 description: 所需能量
   *               required_days:
   *                 type: integer
   *                 description: 所需天数
   *               attribute_bonus:
   *                 type: object
   *                 description: 属性加成配置
   *               special_ability:
   *                 type: object
   *                 description: 特殊能力配置
   *               unlock_reward:
   *                 type: object
   *                 description: 解锁奖励配置
   *               description:
   *                 type: string
   *                 description: 等级描述
   *               sort_order:
   *                 type: integer
   *                 description: 排序顺序
   *               is_active:
   *                 type: boolean
   *                 description: 是否启用
   *     responses:
   *       200:
   *         description: 更新成功
   *       404:
   *         description: 等级配置不存在
   */
  static async updateLevel(ctx) {
    const { id } = ctx.params
    const {
      name, icon, required_energy, required_days,
      attribute_bonus, special_ability, unlock_reward,
      description, sort_order, is_active
    } = ctx.request.body

    try {
      const levelConfig = await PlantLevelConfig.findByPk(id)
      if (!levelConfig) {
        ctx.body = {
          code: 404,
          message: '等级配置不存在'
        }
        return
      }

      const updateData = {}
      if (name !== undefined) updateData.name = name
      if (icon !== undefined) updateData.icon = icon
      if (required_energy !== undefined) updateData.required_energy = required_energy
      if (required_days !== undefined) updateData.required_days = required_days
      if (attribute_bonus !== undefined) updateData.attribute_bonus = JSON.stringify(attribute_bonus)
      if (special_ability !== undefined) updateData.special_ability = JSON.stringify(special_ability)
      if (unlock_reward !== undefined) updateData.unlock_reward = JSON.stringify(unlock_reward)
      if (description !== undefined) updateData.description = description
      if (sort_order !== undefined) updateData.sort_order = sort_order
      if (is_active !== undefined) updateData.is_active = is_active
      updateData.updated_at = new Date()

      await levelConfig.update(updateData)

      ctx.body = {
        code: 200,
        message: '等级配置更新成功',
        data: levelConfig
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新等级配置失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/levels/{id}/toggle:
   *   put:
   *     tags:
   *       - 多肉等级管理
   *     summary: 切换多肉等级配置状态
   *     description: 启用或禁用指定的多肉等级配置
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 等级配置ID
   *     responses:
   *       200:
   *         description: 状态切换成功
   *       404:
   *         description: 等级配置不存在
   */
  static async toggleLevelStatus(ctx) {
    const { id } = ctx.params

    try {
      const levelConfig = await PlantLevelConfig.findByPk(id)
      if (!levelConfig) {
        ctx.body = {
          code: 404,
          message: '等级配置不存在'
        }
        return
      }

      await levelConfig.update({
        is_active: !levelConfig.is_active,
        updated_at: new Date()
      })

      ctx.body = {
        code: 200,
        message: `等级配置已${levelConfig.is_active ? '启用' : '禁用'}`,
        data: levelConfig
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '切换等级配置状态失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/plants/levels/{id}:
   *   delete:
   *     tags:
   *       - 多肉等级管理
   *     summary: 删除多肉等级配置
   *     description: 删除指定的多肉等级配置
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 等级配置ID
   *     responses:
   *       200:
   *         description: 删除成功
   *       404:
   *         description: 等级配置不存在
   *       400:
   *         description: 无法删除（有关联数据）
   */
  static async deleteLevel(ctx) {
    const { id } = ctx.params

    try {
      const levelConfig = await PlantLevelConfig.findByPk(id)
      if (!levelConfig) {
        ctx.body = {
          code: 404,
          message: '等级配置不存在'
        }
        return
      }

      // 这里可以添加检查是否有多肉正在使用该等级的逻辑
      // 暂时允许直接删除，后续可以根据业务需求调整

      await levelConfig.destroy()

      ctx.body = {
        code: 200,
        message: '等级配置删除成功'
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '删除等级配置失败',
        error: error.message
      }
    }
  }
}
