-- Plant Level Config Table Creation Script

DROP TABLE IF EXISTS plant_level_config;
CREATE TABLE plant_level_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    level INT NOT NULL UNIQUE,
    name VARCHAR(64) NOT NULL,
    icon VARCHAR(255) DEFAULT NULL,
    required_energy INT NOT NULL DEFAULT 0,
    required_days INT NOT NULL DEFAULT 0,
    attribute_bonus TEXT DEFAULT NULL,
    special_ability TEXT DEFAULT NULL,
    unlock_reward TEXT DEFAULT NULL,
    description TEXT DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default plant level configurations
INSERT INTO plant_level_config (level, name, icon, required_energy, required_days, attribute_bonus, special_ability, unlock_reward, description, sort_order) VALUES
(1, 'Seedling', '/images/levels/seedling.png', 0, 0,
 '{"growth_speed": 1.0, "energy_efficiency": 1.0}',
 '{"abilities": ["Basic Growth"]}',
 '{"coins": 10, "experience": 5}',
 'A newly sprouted seedling, full of life and hope', 1),

(2, 'Sprout', '/images/levels/sprout.png', 100, 3,
 '{"growth_speed": 1.2, "energy_efficiency": 1.1}',
 '{"abilities": ["Fast Growth", "Basic Photosynthesis"]}',
 '{"coins": 20, "experience": 10, "items": ["fertilizer_basic"]}',
 'A thriving sprout beginning to show unique characteristics', 2),

(3, 'Mature', '/images/levels/mature.png', 300, 7,
 '{"growth_speed": 1.5, "energy_efficiency": 1.3, "resistance": 1.2}',
 '{"abilities": ["Efficient Photosynthesis", "Environmental Adaptation", "Self Repair"]}',
 '{"coins": 50, "experience": 25, "items": ["fertilizer_advanced", "decoration_pot"]}',
 'A mature succulent with complete survival capabilities', 3),

(4, 'Premium', '/images/levels/premium.png', 600, 15,
 '{"growth_speed": 2.0, "energy_efficiency": 1.6, "resistance": 1.5, "beauty": 1.8}',
 '{"abilities": ["Perfect Photosynthesis", "Enhanced Adaptation", "Quick Repair", "Environment Beautification"]}',
 '{"coins": 100, "experience": 50, "items": ["fertilizer_premium", "decoration_premium", "seed_rare"]}',
 'An excellent quality succulent with outstanding appearance and strong vitality', 4),

(5, 'Legendary', '/images/levels/legendary.png', 1200, 30,
 '{"growth_speed": 3.0, "energy_efficiency": 2.0, "resistance": 2.0, "beauty": 2.5, "magic": 1.0}',
 '{"abilities": ["Legendary Photosynthesis", "Perfect Adaptation", "Instant Repair", "Environment Purification", "Aura Emission"]}',
 '{"coins": 200, "experience": 100, "items": ["fertilizer_legendary", "decoration_legendary", "seed_legendary"], "special_title": "Succulent Master"}',
 'A legendary succulent with magical powers, the ultimate goal of all gardeners', 5);

-- Create indexes
CREATE INDEX idx_plant_level_config_level ON plant_level_config(level);
CREATE INDEX idx_plant_level_config_is_active ON plant_level_config(is_active);
CREATE INDEX idx_plant_level_config_sort_order ON plant_level_config(sort_order);

-- Show inserted data
SELECT * FROM plant_level_config ORDER BY sort_order, level;
