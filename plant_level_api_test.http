### 多肉等级管理API测试文件
@admin_token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzU1ODYwODkyLCJleHAiOjE3NTU5NDcyOTJ9.zwh1A3b1L74MKPaHER0lWDf4a8tPUsH0QjpIYNmB5-U

### 1. 管理员登录（获取token）
POST http://localhost:3004/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 2. 获取多肉等级配置列表
GET http://localhost:3004/admin/plants/levels?page=1&limit=10
Authorization: Bearer {{admin_token}}


### 3. 获取多肉等级配置列表（带搜索）
GET http://localhost:3004/admin/plants/levels?page=1&limit=10&search=幼苗
Authorization: Bearer {{admin_token}}

### 4. 获取多肉等级配置列表（按状态筛选）
GET http://localhost:3004/admin/plants/levels?page=1&limit=10&is_active=true
Authorization: Bearer {{admin_token}}

### 5. 获取指定等级配置详情
GET http://localhost:3004/admin/plants/levels/1
Authorization: Bearer {{admin_token}}

### 6. 创建新的等级配置
POST http://localhost:3004/admin/plants/levels
Authorization: Bearer {{admin_token}}
Content-Type: application/json

{
  "level": 6,
  "name": "神话",
  "icon": "/images/levels/mythical.png",
  "required_energy": 2500,
  "required_days": 60,
  "attribute_bonus": {
    "growth_speed": 5.0,
    "energy_efficiency": 3.0,
    "resistance": 3.0,
    "beauty": 4.0,
    "magic": 2.0
  },
  "special_ability": {
    "abilities": ["神话光合作用", "时空适应", "瞬间重生", "环境改造", "灵气爆发", "生命赐予"]
  },
  "unlock_reward": {
    "coins": 500,
    "experience": 200,
    "items": ["fertilizer_mythical", "decoration_mythical", "seed_mythical"],
    "special_title": "多肉之神"
  },
  "description": "超越传说的神话级多肉，拥有改变世界的神奇力量",
  "sort_order": 6
}

### 7. 更新等级配置
PUT http://localhost:3004/admin/plants/levels/1
Authorization: Bearer {{admin_token}}
Content-Type: application/json

{
  "name": "幼苗（更新）",
  "description": "刚刚发芽的小苗，充满生机与希望（已更新）",
  "required_energy": 10
}

### 8. 切换等级配置状态（禁用/启用）
PUT http://localhost:3004/admin/plants/levels/1/toggle
Authorization: Bearer {{admin_token}}

### 9. 删除等级配置
DELETE http://localhost:3004/admin/plants/levels/6
Authorization: Bearer {{admin_token}}

### 10. 获取所有启用的等级配置（用于前端下拉选择）
GET http://localhost:3004/admin/plants/levels?is_active=true&limit=100
Authorization: Bearer {{admin_token}}
